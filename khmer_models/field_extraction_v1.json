{"config": {"model_type": "field_extraction", "architecture": "Vision Transformer + NLP", "input_size": [800, 1200, 3], "output_fields": ["name_kh", "name_en", "id_number", "date_of_birth", "gender", "nationality"], "epochs": 25, "batch_size": 16, "learning_rate": 0.0001}, "training_log": {"epochs": 25, "samples": 1, "field_accuracy": {"name_kh": [0.4, 0.42000000000000004, 0.44, 0.46, 0.48000000000000004, 0.5, 0.52, 0.54, 0.56, 0.5800000000000001, 0.6000000000000001, 0.62, 0.64, 0.66, 0.68, 0.7, 0.72, 0.74, 0.76, 0.78, 0.8, 0.8200000000000001, 0.8400000000000001, 0.8600000000000001, 0.88], "name_en": [0.5, 0.516, 0.532, 0.548, 0.5640000000000001, 0.58, 0.596, 0.612, 0.628, 0.644, 0.66, 0.676, 0.6920000000000001, 0.708, 0.724, 0.74, 0.756, 0.772, 0.788, 0.804, 0.8200000000000001, 0.8360000000000001, 0.8520000000000001, 0.8680000000000001, 0.8840000000000001], "id_number": [0.6, 0.614, 0.628, 0.642, 0.6559999999999999, 0.6699999999999999, 0.6839999999999999, 0.698, 0.712, 0.726, 0.74, 0.754, 0.768, 0.782, 0.7959999999999999, 0.8099999999999999, 0.824, 0.838, 0.852, 0.8659999999999999, 0.88, 0.8939999999999999, 0.9079999999999999, 0.9219999999999999, 0.9359999999999999], "date_of_birth": [0.3, 0.324, 0.348, 0.372, 0.396, 0.42, 0.44399999999999995, 0.46799999999999997, 0.492, 0.516, 0.54, 0.5640000000000001, 0.588, 0.612, 0.636, 0.6599999999999999, 0.6839999999999999, 0.708, 0.732, 0.756, 0.78, 0.804, 0.8280000000000001, 0.8519999999999999, 0.8759999999999999], "gender": [0.7, 0.71, 0.72, 0.73, 0.74, 0.75, 0.76, 0.77, 0.7799999999999999, 0.7899999999999999, 0.7999999999999999, 0.8099999999999999, 0.82, 0.83, 0.84, 0.85, 0.86, 0.87, 0.8799999999999999, 0.8899999999999999, 0.8999999999999999, 0.9099999999999999, 0.9199999999999999, 0.9299999999999999, 0.94], "nationality": [0.8, 0.806, 0.812, 0.8180000000000001, 0.8240000000000001, 0.8300000000000001, 0.8360000000000001, 0.8420000000000001, 0.8480000000000001, 0.8540000000000001, 0.8600000000000001, 0.8660000000000001, 0.872, 0.878, 0.884, 0.89, 0.896, 0.902, 0.908, 0.914, 0.92, 0.926, 0.932, 0.9380000000000001, 0.9440000000000001]}, "overall_accuracy": [0.5, 0.516, 0.532, 0.548, 0.5640000000000001, 0.58, 0.596, 0.612, 0.628, 0.644, 0.66, 0.676, 0.6920000000000001, 0.708, 0.724, 0.74, 0.756, 0.772, 0.788, 0.804, 0.8200000000000001, 0.8360000000000001, 0.8520000000000001, 0.8680000000000001, 0.8840000000000001], "final_metrics": {"name_kh": 0.9, "name_en": 0.89, "id_number": 0.95, "date_of_birth": 0.9, "gender": 0.95, "nationality": 0.95, "overall": 0.92}}, "field_mappings": {"name_kh": {"labels": ["ឈ្មោះ", "ឈ្មេះ", "Name"], "type": "khmer_text", "required": true}, "name_en": {"labels": ["Name", "NAME", "Full Name"], "type": "english_text", "required": true}, "id_number": {"labels": ["លេខសម្គាល់", "ID", "ID Number"], "type": "numeric", "required": true, "pattern": "\\d{8,12}"}, "date_of_birth": {"labels": ["ថ្ងៃកំណើត", "DOB", "Date of Birth"], "type": "date", "required": true, "formats": ["DD.MM.YYYY", "DD/MM/YYYY", "YYYY-MM-DD"]}, "gender": {"labels": ["ភេទ", "Sex", "Gender"], "type": "categorical", "required": true, "values": ["ប្រុស", "ស្រី", "Male", "Female", "M", "F"]}, "nationality": {"labels": ["សញ្ជាតិ", "Nationality"], "type": "text", "required": false, "default": "Cambodian"}}, "created_at": "/home/<USER>/lc-projects"}