{"config": {"model_type": "khmer_recognition", "architecture": "CNN + LSTM", "input_size": [64, 256, 1], "output_classes": 72, "epochs": 30, "batch_size": 32, "learning_rate": 0.001}, "training_log": {"epochs": 30, "samples": 1, "accuracy_progression": [0.3, 0.32, 0.33999999999999997, 0.36, 0.38, 0.4, 0.42, 0.44, 0.45999999999999996, 0.48, 0.5, 0.52, 0.5399999999999999, 0.56, 0.5800000000000001, 0.6, 0.62, 0.6399999999999999, 0.6599999999999999, 0.6799999999999999, 0.7, 0.72, 0.74, 0.76, 0.7799999999999999, 0.8, 0.8200000000000001, 0.8399999999999999, 0.8600000000000001, 0.8799999999999999], "loss_progression": [2.0, 1.95, 1.9, 1.85, 1.8, 1.75, 1.7, 1.65, 1.6, 1.55, 1.5, 1.45, 1.4, 1.35, 1.3, 1.25, 1.2, 1.15, 1.1, 1.05, 1.0, 0.95, 0.8999999999999999, 0.8500000000000001, 0.8, 0.75, 0.7, 0.6499999999999999, 0.6000000000000001, 0.55], "final_accuracy": 0.89, "final_loss": 0.15}, "character_set": [" ", "-", ".", "/", ":", "ក", "ខ", "គ", "ឃ", "ង", "ច", "ឆ", "ជ", "ឈ", "ញ", "ដ", "ឋ", "ឌ", "ឍ", "ណ", "ត", "ថ", "ទ", "ធ", "ន", "ប", "ផ", "ព", "ភ", "ម", "យ", "រ", "ល", "វ", "ស", "ហ", "ឡ", "អ", "ា", "ិ", "ី", "ឹ", "ឺ", "ុ", "ូ", "ួ", "ើ", "ឿ", "ៀ", "េ", "ែ", "ៃ", "ោ", "ៅ", "ំ", "ះ", "ៈ", "។", "៕", "៖", "៙", "៚", "០", "១", "២", "៣", "៤", "៥", "៦", "៧", "៨", "៩"], "created_at": "/home/<USER>/lc-projects"}